import React, { useState } from 'react';
import {
  PlayIcon,
  PauseIcon,
  SpeakerWaveIcon,
  SpeakerXMarkIcon,
  ArrowsPointingOutIcon,
  EyeIcon
} from '@heroicons/react/24/outline';
import { 
  VideoGenerationProject,
  VideoGenerationConfig,
  MaterialCategory,
  MATERIAL_CATEGORY_CONFIG
} from '../../types/videoGeneration';

interface CentralVideoPreviewProps {
  project: VideoGenerationProject;
  onConfigChange?: (config: VideoGenerationConfig) => void;
}

/**
 * 中央视频预览组件
 * 专门用于工作台中央预览区域
 */
export const CentralVideoPreview: React.FC<CentralVideoPreviewProps> = ({
  project,
  onConfigChange
}) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);

  // 获取所有选中的素材
  const getAllSelectedAssets = () => {
    const allAssets: any[] = [];
    Object.entries(project.selected_assets).forEach(([category, assets]) => {
      if (assets && assets.length > 0) {
        allAssets.push(...assets.map(asset => ({ ...asset, category: category as MaterialCategory })));
      }
    });
    return allAssets;
  };

  const selectedAssets = getAllSelectedAssets();

  // 模拟播放控制
  const handlePlayPause = () => {
    setIsPlaying(!isPlaying);
  };

  const handleMuteToggle = () => {
    setIsMuted(!isMuted);
  };

  const handleSeek = (e: React.MouseEvent<HTMLDivElement>) => {
    const rect = e.currentTarget.getBoundingClientRect();
    const percent = (e.clientX - rect.left) / rect.width;
    const newTime = percent * project.generation_config.duration;
    setCurrentTime(Math.max(0, Math.min(newTime, project.generation_config.duration)));
  };

  return (
    <div className="h-full flex flex-col">
      {/* 预览标题 */}
      <div className="flex-shrink-0 flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center gap-2">
          <EyeIcon className="h-5 w-5 text-primary-600" />
          <h3 className="text-lg font-medium text-gray-900">视频预览</h3>
        </div>
        <div className="text-sm text-gray-500">
          {project.generation_config.resolution} • {project.generation_config.frame_rate}fps • {project.generation_config.duration}s
        </div>
      </div>

      {/* 主预览区域 */}
      <div className="flex-1 flex items-center justify-center p-6">
        <div className="w-full max-w-4xl">
          {/* 视频预览窗口 */}
          <div className="aspect-video bg-gradient-to-br from-gray-900 to-gray-800 rounded-lg overflow-hidden relative shadow-lg">
            {/* 模拟视频内容 */}
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="text-center text-white">
                <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mb-4 mx-auto">
                  <PlayIcon className="h-10 w-10" />
                </div>
                <p className="text-xl font-medium">视频预览</p>
                <p className="text-sm text-gray-300 mt-2">
                  已选择 {selectedAssets.length} 个素材
                </p>
              </div>
            </div>

            {/* 播放控制覆盖层 */}
            <div className="absolute inset-0 bg-black/0 hover:bg-black/20 transition-colors duration-200 group">
              <div className="absolute bottom-4 left-4 right-4">
                <div className="flex items-center justify-between text-white mb-3">
                  {/* 播放控制 */}
                  <div className="flex items-center gap-3">
                    <button
                      onClick={handlePlayPause}
                      className="w-12 h-12 bg-white/20 hover:bg-white/30 rounded-full flex items-center justify-center transition-colors duration-200"
                    >
                      {isPlaying ? (
                        <PauseIcon className="h-6 w-6" />
                      ) : (
                        <PlayIcon className="h-6 w-6 ml-1" />
                      )}
                    </button>
                    
                    <button
                      onClick={handleMuteToggle}
                      className="w-10 h-10 hover:bg-white/20 rounded-full flex items-center justify-center transition-colors duration-200"
                    >
                      {isMuted ? (
                        <SpeakerXMarkIcon className="h-5 w-5" />
                      ) : (
                        <SpeakerWaveIcon className="h-5 w-5" />
                      )}
                    </button>

                    {/* 时间显示 */}
                    <div className="text-sm">
                      {Math.floor(currentTime)}s / {project.generation_config.duration}s
                    </div>
                  </div>

                  {/* 全屏按钮 */}
                  <button className="w-10 h-10 hover:bg-white/20 rounded-full flex items-center justify-center transition-colors duration-200">
                    <ArrowsPointingOutIcon className="h-5 w-5" />
                  </button>
                </div>

                {/* 进度条 */}
                <div 
                  className="w-full bg-white/20 rounded-full h-2 cursor-pointer"
                  onClick={handleSeek}
                >
                  <div 
                    className="bg-primary-500 h-2 rounded-full transition-all duration-200"
                    style={{ width: `${(currentTime / project.generation_config.duration) * 100}%` }}
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 选中素材概览 */}
      <div className="flex-shrink-0 border-t border-gray-200 p-4">
        <div className="flex items-center justify-between mb-3">
          <h4 className="text-sm font-medium text-gray-900">选中素材</h4>
          <span className="text-xs text-gray-500">{selectedAssets.length} 个素材</span>
        </div>

        {selectedAssets.length === 0 ? (
          <div className="text-center py-4 text-gray-500">
            <p className="text-sm">请从左侧素材区选择素材</p>
          </div>
        ) : (
          <div className="grid grid-cols-6 gap-2 max-h-32 overflow-y-auto custom-scrollbar">
            {Object.values(MaterialCategory).map((category) => {
              const categoryAssets = project.selected_assets[category] || [];
              if (categoryAssets.length === 0) return null;

              const config = MATERIAL_CATEGORY_CONFIG[category];
              
              return categoryAssets.map((asset) => (
                <div key={asset.id} className="group relative">
                  <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden">
                    {asset.thumbnail_path ? (
                      <img
                        src={asset.thumbnail_path}
                        alt={asset.name}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center">
                        <span className="text-lg">{config.icon}</span>
                      </div>
                    )}
                  </div>
                  
                  {/* 素材信息提示 */}
                  <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity duration-200 rounded-lg flex items-center justify-center">
                    <div className="text-center text-white">
                      <div className="text-xs font-medium truncate px-1">{asset.name}</div>
                      <div className="text-xs text-gray-300">{config.label}</div>
                    </div>
                  </div>
                </div>
              ));
            })}
          </div>
        )}
      </div>

      {/* 生成统计 */}
      <div className="flex-shrink-0 bg-gradient-to-r from-primary-50 to-primary-100 border-t border-primary-200 p-3">
        <div className="grid grid-cols-4 gap-4 text-center">
          <div>
            <div className="text-lg font-bold text-primary-700">{selectedAssets.length}</div>
            <div className="text-xs text-primary-600">选中素材</div>
          </div>
          <div>
            <div className="text-lg font-bold text-primary-700">{project.generation_config.duration}s</div>
            <div className="text-xs text-primary-600">视频时长</div>
          </div>
          <div>
            <div className="text-lg font-bold text-primary-700">
              {(() => {
                const baseSize = project.generation_config.duration * 2;
                const qualityMultiplier = project.generation_config.quality === 'high' ? 2 : 
                                        project.generation_config.quality === 'medium' ? 1.5 : 1;
                const resolutionMultiplier = project.generation_config.resolution === '4k' ? 4 : 
                                           project.generation_config.resolution === '1080p' ? 2 : 1;
                return `${(baseSize * qualityMultiplier * resolutionMultiplier).toFixed(1)}MB`;
              })()}
            </div>
            <div className="text-xs text-primary-600">预估大小</div>
          </div>
          <div>
            <div className="text-lg font-bold text-primary-700">
              {project.generation_config.quality === 'high' ? '高' : 
               project.generation_config.quality === 'medium' ? '中' : '低'}
            </div>
            <div className="text-xs text-primary-600">输出质量</div>
          </div>
        </div>
      </div>
    </div>
  );
};
